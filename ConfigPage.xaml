<?xml version="1.0" encoding="utf-8"?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Aila.ConfigPage">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <Editor x:Name="editor" Grid.Row="0" Grid.Column="0"  Grid.ColumnSpan="3"
                    VerticalOptions="FillAndExpand" 
                    HorizontalOptions="FillAndExpand" 
                    Placeholder="Enter text here" 
                    BackgroundColor="Transparent"/>
            
            <Button Grid.Row="0" Grid.Column="1"
                    HorizontalOptions="End" 
                    VerticalOptions="End" 
                    Margin="0,0,10,10" 
                    FontSize="Medium"
                    WidthRequest="200"
                    HeightRequest="50"
                    Text="Latest Config" 
                    ToolTipProperties.Text="Click to check configuration update"
                    Clicked="OnCheckConfigUpdateClicked"/>
            
            <Button Grid.Row="0" Grid.Column="2" 
                    HorizontalOptions="End" 
                    VerticalOptions="End" 
                    Margin="0,0,10,10" 
                    FontSize="Medium"
                    WidthRequest="100"
                    HeightRequest="50"
                    Text="↓ Save" 
                    ToolTipProperties.Text="Click to Save your data"
                    Clicked="OnSaveButtonClicked"/>
        </Grid>
    </ContentPage.Content>
</ContentPage>



