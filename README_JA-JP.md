<div align="center">
  <p><strong>一度にすべての主要なAIと対話し、究極の洞察を明らかにする</strong></p>

 [英語](README.md) | [简体中文](README_ZH-CN.md) | [繁體中文](README_ZH-TW.md) | 日本語 | [한국어](README_KO-KR.md) | [Français](README_FR-FR.md)

[![GitHub Codespacesで開く](https://github.com/codespaces/badge.svg)](https://github.com/win4r/AISuperDomain/releases)

</div>

![画像](https://github.com/win4r/AISuperDomain/assets/42172631/2115997d-8b00-4767-bf79-103b4e53abc3)


# AI超元域デスクトップアプリケーション

AI超元域デスクトップアプリケーションのGitHubリポジトリへようこそ！この革新的なソフトウェアは、複数の主要なAIモデルを一つのデスクトップアプリケーションに統合し、ユーザーが様々な質問に対して多様な回答を生成するための強力なプラットフォームを提供します。AI超元域は、WindowsおよびmacOSシステム上でシームレスに動作するように設計されており、ユーザーが一つの便利なインターフェースを通じて複数の人工知能と対話できるようにします。

## 特徴

### 複数AI統合
AI超元域は、ユーザーが質問を投げかけ、複数のAIモデルから同時に回答を受け取ることができるというユニークな機能によって、ユーザー体験を豊かにします。各AIは独自の視点と専門知識を提供し、幅広い洞察をもたらします。

### サポートされているAIモデル
AI超元域は現在、以下のAIモデルをサポートしています：

| AI 名称             | 紹介 |
|---------------------|--------------------------------------------------|
| ChatGPT             | OpenAI によって開発されたAIで、提供された入力に基づいて人間のようなテキストを生成することで知られています。|
| Gemini              | 言語翻訳やデータ分析を含む多様なタスクを実行できる多目的AI。|
| Claude              | 会話参加のために設計されたAIで、人間の対話を模倣することができる応答を提供します。|
| Copilot             | 情報をナビゲートし、さまざまなドメインでタスクを実行するのを助けるあなたのAIコンパニオン。|
| Poe                 | 流れるような会話とコンテンツ生成のための高度なAIチャットボットアグリゲータ。|
| Perplexity          | 詳細な説明を伴う複雑な質問に答えることに焦点を当てたAI。|
| HuggingChat         | Hugging Faceプラットフォーム上で構築され、自然言語処理能力で知られるAI。|
| YouAI               | 個々のユーザーの好みに合わせた応答と推奨事項をカスタマイズするパーソナライズされたAI。|
| lmsyshat            | システム管理とITサポートにおける能力を持つAIで、技術的な支援を提供します。|
| Gemini Pro 1.5      | 強化された性能と追加機能を提供するGeminiの高度なバージョン。|
| Together AI         | 共有タスクとプロジェクト管理ツールを通じて協力とチームワークを促進するAI。|
| Grok AI             | パターン認識とデータ解釈に秀でたAIで、分析によく使用されます。|
| ai playground       | 異なるAIモデルを実験し、その機能を探求するためのプラットフォーム。|
| PI AI               | 個人情報を安全に管理するのを助ける個人知能を専門とするAI。|
| Devin AI            | 開発とコーディングの支援を提供し、ソフトウェア開発プロセスを合理化するAI。|
| Tongyi Qianwen      | 多様なデータソースを統一して包括的な洞察を提供することに焦点を当てたAI。|
| DouBao AI           | フィンテック分野で一般的に使用される、財務分析と予測を提供するAI。|
| ChatGLM             | 生成言語モデルに基づいてテキストを生成するAIで、様々なアプリケーションに適しています。|
| character ai        | ストーリーテリングやゲームの目的でキャラクタープロファイルと物語を作成するAI。|
| meta image generator| テキストの説明に基づいて画像を生成するAIで、クリエイティブなプロジェクトによく使用されます。|
| Suno AI             | ユーザー入力に基づいて音楽を作曲し、曲を生成するAIで、音楽の創造性を高めます。|


### 動的AI表示
同時に表示するAIの回答の数を選択することで、視聴体験をカスタマイズできます。1、2、3、4、または6のAIモデルを表示するオプションがあり、情報の提示方法に柔軟性を提供します。

### フルスクリーンモード
右上隅にあるAIの名前をクリックすると、その回答をフルスクリーンで表示でき、焦点を合わせて読みやすくなります。多AI表示に戻るには、単に「ホーム」ボタンをクリックします。

### 効率的なインタラクション
- **プロンプトの提案**：クエリを開始するには「/」でアクセスできるプロンプトの提案リストを使用し、より迅速に参加できます。
- **永続的プロンプト**：入力の最初に「#」を使用すると、永続的プロンプトがアクティブになり、継続的に使用できます。
- **柔軟な送信**：Tab+Enterのショートカットを使用するか、「送信」ボタンをクリックして質問を送信できます。表示されているAIモデルを簡単に切り替えるための「スイッチ」ボタンがあります。

### カスタマイズと設定
左上のアイコンをクリックすると表示されるフライアウトメニューを通じて、設定とカスタマイズオプションにアクセスできます。ここでは、好みのAIモデルを選択し、表示数を調整できます。「設定ファイル」オプションはAI超元域のJSON設定ファイルにつながり、新しいAIモデルを追加したり、プロンプトを変更したり、経験をパーソナライズしたりできます。

## はじめに

AI超元域を使用するには、最新バージョンを[Aila Releases](https://github.com/win4r/AISuperDomain/releases)からダウンロードしてください。お使いのオペレーティングシステムに基づいて以下の指示に従って、スムーズなインストールプロセスを確実にします。

### macOSのインストール
ダウンロード後、macOSユーザーはダウンロードしたファイルをダブルクリックするだけでインストールプロセスを開始できます。画面の指示に従ってセットアップを完了してください。

### Windowsのインストール
Windowsユーザーは、インストールに先立ってシステムに.NET 8がインストールされていることを確認する必要があります。さらに、AI超元域のインストールを進める前に提供された証明書をインストールする必要があります。これらの前提条件を満たしたら、ダウンロードしたファイルをダブルクリックして、画面の指示に従ってAI超元域をインストールしてください。

AI超元域内の設定を探索して、AI体験をお好みにカスタマイズしてください。
