<div align="center">
  <p><strong>一次提问，多个AI同时回答</strong></p>

 [英语](README.md) | 简体中文 | [繁體中文](README_ZH-TW.md) | [日本語](README_JA-JP.md) | [한국어](README_KO-KR.md) | [Français](README_FR-FR.md)

[![在GitHub Codespaces中打开](https://github.com/codespaces/badge.svg)](https://github.com/win4r/AISuperDomain/releases)

</div>

![图片](https://github.com/win4r/AISuperDomain/assets/42172631/2115997d-8b00-4767-bf79-103b4e53abc3)

### 🔥视频演示：https://b23.tv/B1OXY2V

# AI超元域桌面应用

欢迎来到AI超元域桌面应用的GitHub仓库！这款创新的软件集成了多个领先的AI模型于一体的桌面应用，为用户提供了一个强大的平台，以产生多样化的回应。AI超元域设计用于在Windows和macOS系统上无缝运行，使用户能够通过一个便捷的界面与多个人工智能进行互动。

## 特点

### 多AI集成
AI超元域的独特之处在于，它允许用户提出问题并同时从多个AI模型中获得回答。这一独特功能丰富了用户体验，因为每个AI都贡献了其独特的视角和专长。

### 支持的AI模型
AI超元域目前支持以下AI模型：

| AI名称             | 简介 |
|--------------------|--------------|
| ChatGPT            | 由OpenAI开发的AI，以基于提供的输入生成类似人类的文本而闻名。 |
| Gemini             | 一种多功能AI，能够执行多种任务，包括语言翻译和数据分析。 |
| Claude             | 专为对话参与设计的AI，提供能模仿人类对话的回应。 |
| Copilot            | 你的AI伴侣，帮助你在各个领域导航信息和执行任务。 |
| Poe                | 一个高级AI聊天机器人聚合器，用于无缝对话和内容生成。 |
| Perplexity         | 一种专注于用详细解释回答复杂问题的AI。 |
| HuggingChat        | 基于Hugging Face平台构建的AI，以其自然语言处理能力而闻名。 |
| YouAI              | 一种个性化AI，根据用户的偏好定制响应和推荐。 |
| lmsyshat           | 一种在系统管理和IT支持方面具有能力的AI，提供技术帮助。 |
| Gemini Pro 1.5     | Gemini的高级版本，提供更高的性能和额外的功能。 |
| Together AI        | 通过共享任务和项目管理工具促进协作和团队合作的AI。 |
| Grok AI            | 在模式识别和数据解释方面表现出色的AI，常用于分析。 |
| ai playground      | 用于实验不同AI模型并探索其功能的平台。 |
| PI AI              | 专注于个人智能的AI，帮助用户安全管理个人数据。 |
| Devin AI           | 提供开发和编码帮助的AI，简化软件开发过程。 |
| Tongyi Qianwen     | 专注于统一多样化数据源以提供全面洞察的AI。 |
| DouBao AI          | 提供财务分析和预测的AI，常用于金融科技领域。 |
| ChatGLM            | 基于生成语言模型生成文本的AI，适用于各种应用。 |
| character ai       | 为讲故事和游戏目的创建角色档案和叙事的AI。 |
| meta image generator | 根据文字描述生成图像的AI，常用于创意项目。 |
| Suno AI            | 根据用户输入创作音乐和生成歌曲的AI，增强音乐创造力。 |


### 动态AI显示
通过选择同时显示多少AI回应来自定义您的观看体验。选项包括1, 2, 3, 4或6个AI模型，允许在信息展示上的灵活性。

### 全屏模式
点击右上角的AI名称，可将其回应全屏查看，便于集中阅读。要返回到多AI显示，只需点击“Home”按钮即可。

### 高效互动
- **提示建议**：用“/”启动查询可访问提示建议列表，便于更快地进行互动。
- **持续提示**：在输入的开始处使用“#”可以激活持续提示功能，保持它可用于持续使用。
- **灵活提交**：通过Tab+Enter快捷键或点击“提交”按钮提交问题。 “切换”按钮允许轻松地通过显示的AI模型循环。

### 自定义和配置
通过点击左上角的图标访问飞出菜单中的设置和自定义选项。在这里，用户可以选择他们偏好的AI模型并调整显示计数。“配置文件”选项导致Aila的JSON配置文件，用户可以在其中添加新的AI模型，修改提示，并个性化他们的体验。

## 入门

要开始使用AI超元域，请从[Aila发布版](https://github.com/win4r/AISuperDomain/releases)下载最新版本。根据您的操作系统，遵循下面的指南以确保顺利安装过程。

### macOS安装
下载后，macOS用户可以简单地双击下载的文件开始安装过程。按照屏幕上的指示完成设置。

### Windows安装
Windows用户必须确保在安装前他们的系统已安装.NET 8。此外，您需要在继续Aila安装之前安装所提供的证书。满足这些先决条件后，双击下载的文件并按照屏幕上的指示安装Aila。

探索Aila内的设置，根据您的喜好自定义您的AI体验。

## 贡献

我们鼓励对AI超元域的贡献。无论您有新功能的建议，改进，还是发现了bug，请随时开设问题或提交拉取请求。

## 许可证

AI超元域桌面应用在MIT许可证下发布。更多细节，请查看LICENSE文件。

## 联系方式

如需支持或反馈，请通过这个仓库的GitHub问题部分与我们联系。您的反馈对于让AI超元域成为一个更好的与AI互动的工具至关重要。

感谢您探索AI超元域桌面应用。我们期待看到您使用这个平台以创新的方式增强您的AI互动体验！

## 请我喝咖啡
[!["Buy Me A Coffee"](https://storage.ko-fi.com/cdn/kofi2.png?v=3)](https://ko-fi.com/aila)

## 我的微信群组

<img src="https://github.com/win4r/AISuperDomain/assets/42172631/d6dcfd1a-60fa-4b6f-9d5e-1482150a7d95" width="186" height="300">
<img src="https://github.com/win4r/AISuperDomain/assets/42172631/7568cf78-c8ba-4182-aa96-d524d903f2bc" width="214.8" height="291">
<img src="https://github.com/win4r/AISuperDomain/assets/42172631/fefe535c-8153-4046-bfb4-e65eacbf7a33" width="207" height="281">
