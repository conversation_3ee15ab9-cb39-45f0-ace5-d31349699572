<div align="center">
  <p><strong>一次提問，多個AI同時回答。
</strong></p>

 [英語](README.md) | [简体中文](README_ZH-CN.md) | 繁體中文 | [日本語](README_JA-JP.md) | [한국어](README_KO-KR.md) | [Français](README_FR-FR.md)

[![在GitHub Codespaces中打開](https://github.com/codespaces/badge.svg)](https://github.com/win4r/AISuperDomain/releases)

</div>

![圖片](https://github.com/win4r/AISuperDomain/assets/42172631/2115997d-8b00-4767-bf79-103b4e53abc3)

### 🔥视频演示：https://b23.tv/B1OXY2V


# AI超元域桌面應用

歡迎來到AI超元域桌面應用的GitHub倉庫！這款創新的軟件集成了多個領先的AI模型於一體的桌面應用，為用戶提供了一個強大的平台，以產生多樣化的回應。AI超元域設計用於在Windows和macOS系統上無縫運行，使用戶能夠通過一個便捷的介面與多個人工智能進行互動。

## 特點

### 多AI集成
AI超元域的獨特之處在於，它允許用戶提出問題並同時從多個AI模型中獲得回答。這一獨特功能豐富了用戶體驗，因為每個AI都貢獻了其獨特的視角和專長。

### 支援的AI模型
AI超元域目前支援以下AI模型：

| AI 名稱             | 簡介 |
|-------------------|------|
| ChatGPT           | 一款由OpenAI開發的AI，以根據提供的輸入生成類似人類的文字而聞名。|
| Gemini            | 一款多功能AI，能夠執行包括語言翻譯和數據分析在內的多種任務。|
| Claude            | 一款專為對話參與而設計的AI，提供能模仿人類對話的回應。|
| Copilot           | 你的AI夥伴，幫助你在各個領域導航信息並執行任務。|
| Poe               | 一款先進的AI聊天機器人聚合器，用於無縫對話和內容生成。|
| Perplexity        | 一款專注於用詳細解釋回答複雜問題的AI。|
| HuggingChat       | 一款建立在Hugging Face平台上的AI，以其自然語言處理能力而聞名。|
| YouAI             | 一款根據個別用戶偏好量身定制回應和推薦的個性化AI。|
| lmsyshat          | 一款在系統管理和IT支持方面具有能力的AI，提供技術援助。|
| Gemini Pro 1.5    | Gemini的高級版本，提供了提升的性能和額外的功能。|
| Together AI       | 一款通過共享任務和項目管理工具促進協作和團隊合作的AI。|
| Grok AI           | 一款在模式識別和數據解釋方面表現出色的AI，經常用於分析。|
| ai playground     | 一個用於實驗不同AI模型並探索其功能的平台。|
| PI AI             | 一款專門在個人智能方面，幫助用戶安全管理個人數據的AI。|
| Devin AI          | 一款提供開發和編碼輔助，簡化軟件開發過程的AI。|
| Tongyi Qianwen    | 一款專注於統一多元數據源以提供全面洞察的AI。|
| DouBao AI         | 一款提供財經分析和預測的AI，常用於金融科技領域。|
| ChatGLM           | 一款基於生成語言模型生成文本的AI，適用於各種應用。|
| character ai      | 一款為故事講述和遊戲目的創建角色檔案和敘事的AI。|
| meta image generator | 一款根據文字描述生成圖片的AI，經常用於創意項目。|
| Suno AI           | 一款根據用戶輸入作曲和生成歌曲的AI，增強音樂創意。|


### 動態AI顯示
通過選擇同時顯示多少AI回應來自定義您的觀看體驗。選項包括1, 2, 3, 4或6個AI模型，允許在信息展示上的靈活性。

### 全屏模式
點擊右上角的AI名稱，可將其回應全屏查看，便於集中閱讀。要返回到多AI顯示，只需點擊“Home”按鈕即可。

### 高效互動
- **提示建議**：用“/”啟動查詢可訪問提示建議列表，便於更快地進行互動。
- **持續提示**：在輸入的開始處使用“#”可以激活持續提示功能，保持它可用於持續使用。
- **靈活提交**：通過Tab+Enter快捷鍵或點擊“提交”按鈕提交問題。 “切換”按鈕允許輕鬆地通過顯示的AI模型循環。

### 自定義和配置
通過點擊左上角的圖標訪問飛出菜單中的設置和自定義選項。在這裡，用戶可以選擇他們偏好的AI模型並調整顯示計數。“配置文件”選項導致Aila的JSON配置文件，用戶可以在其中添加新的AI模型，修改提示，並個性化他們的體驗。

## 入門

要開始使用AI超元域，請從[Aila發布版](https://github.com/win4r/AISuperDomain/releases)下載最新版本。根據您的操作系統，遵循下面的指南以確保順利安裝過程。

### macOS安裝
下載後，macOS用戶可以簡單地雙擊下載的文件開始安裝過程。按照屏幕上的指示完成設置。

### Windows安裝
Windows用戶必須確保在安裝前他們的系統已安裝.NET 8。此外，您需要在繼續Aila安裝之前安裝所提供的證書。滿足這些先決條件後，雙擊下載的文件並按照屏幕上的指示安裝Aila。

探索Aila內的設置，根據您的喜好自定義您的AI體驗。

## 貢獻

我們鼓勵對AI超元域的貢獻。無論您有新功能的建議，改進，還是發現了bug，請隨時開設問題或提交拉取請求。

## 許可證

AI超元域桌面應用在MIT許可證下發布。更多細節，請查看LICENSE文件。

## 聯絡方式

如需支持或反饋，請通過這個倉庫的GitHub問題部分與我們聯繫。您的反饋對於讓AI超元域成為一個更好的與AI互動的工具至關重要。

感謝您探索AI超元域桌面應用。我們期待看到您使用這個平台以創新的方式增強您的AI互動體驗！

## 請我喝咖啡
[!["Buy Me A Coffee"](https://storage.ko-fi.com/cdn/kofi2.png?v=3)](https://ko-fi.com/aila)

## 我的微信群

<img src="https://github.com/win4r/AISuperDomain/assets/42172631/d6dcfd1a-60fa-4b6f-9d5e-1482150a7d95" width="186" height="300">
<img src="https://github.com/win4r/AISuperDomain/assets/42172631/7568cf78-c8ba-4182-aa96-d524d903f2bc" width="214.8" height="291">
<img src="https://github.com/win4r/AISuperDomain/assets/42172631/fefe535c-8153-4046-bfb4-e65eacbf7a33" width="207" height="281">

