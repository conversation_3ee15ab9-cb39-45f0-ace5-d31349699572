﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Aila.MainPage"
             Title="AI超元域">

    <!-- <AbsoluteLayout> -->
    <!--     <Grid x:Name="_grid" -->
    <!--           AbsoluteLayout.LayoutBounds="0, 0, 1, 1" -->
    <!--           AbsoluteLayout.LayoutFlags="All"> -->
    <!--         <Grid.ColumnDefinitions> -->
    <!--             <ColumnDefinition Width="*" /> -->
    <!--             <ColumnDefinition Width="*" /> -->
    <!--             <ColumnDefinition Width="*" /> -->
    <!--             <ColumnDefinition Width="*" /> -->
    <!--             <ColumnDefinition Width="*" /> -->
    <!--             <ColumnDefinition Width="*" /> -->
    <!--             <ColumnDefinition Width="*" /> -->
    <!--             <ColumnDefinition Width="*" /> -->
    <!--             <ColumnDefinition Width="*" /> -->
    <!--             <ColumnDefinition Width="*" /> -->
    <!--             <ColumnDefinition Width="*" /> -->
    <!--             <ColumnDefinition Width="*" /> -->
    <!--         </Grid.ColumnDefinitions> -->
    <!--         <Grid.RowDefinitions> -->
    <!--             <RowDefinition Height="*" /> -->
    <!--             <RowDefinition Height="Auto" /> -->
    <!--         </Grid.RowDefinitions> -->
    <!-- -->
    <!--         ~1~ Editor 占据前10列 @1@ -->
    <!--         <Editor  -->
    <!--             x:Name="_editor" -->
    <!--             Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="10" -->
    <!--                 Placeholder="👉👉👉Enter your question here..." -->
    <!--                 VerticalOptions="Start" -->
    <!--                 FontSize="18" -->
    <!--                 AutoSize="TextChanges"/> -->
    <!-- -->
    <!--         ~1~ Submit Button 占据第11列 @1@ -->
    <!--         <Button -->
    <!--             x:Name="SubmitBtn" -->
    <!--             Grid.Row="1" Grid.Column="10" Text="➡ Submit" -->
    <!--                 HeightRequest="50" -->
    <!--                 VerticalOptions="End" -->
    <!--                 Clicked="OnSubmitClicked" /> -->
    <!-- -->
    <!--         ~1~ Switch Button 占据第12列 @1@ -->
    <!--         <Button  -->
    <!--             x:Name="SwitchBtn" -->
    <!--             Grid.Row="1" Grid.Column="11" Text="↩ Switch" -->
    <!--                 HeightRequest="50" -->
    <!--                 VerticalOptions="End" -->
    <!--                 Clicked="OnSwitchClicked" /> -->
    <!-- -->
    <!--     </Grid> -->
    <!-- </AbsoluteLayout> -->

    
</ContentPage>
