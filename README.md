<div align="center">
  <p><strong>Engage with Every Leading AI at Once, Uncover the Ultimate Insights</strong></p>

 English | [简体中文](README_ZH-CN.md) | [繁體中文](README_ZH-TW.md) | [日本語](README_JA-JP.md) | [한국어](README_KO-KR.md) | [Français](README_FR-FR.md)

[![Open in GitHub Codespaces](https://github.com/codespaces/badge.svg)](https://github.com/win4r/AISuperDomain/releases)

</div>

![image](https://github.com/win4r/AISuperDomain/assets/42172631/2115997d-8b00-4767-bf79-103b4e53abc3)

### 🔥视频演示：https://b23.tv/B1OXY2V


# Aila Desktop Application

Welcome to the Aila Desktop Application GitHub repository! This innovative software integrates multiple leading AI models into a single desktop application, providing users with a powerful platform for generating diverse responses to their inquiries. Aila is designed to run seamlessly on both Windows and macOS systems, enabling users to interact with multiple artificial intelligences through one convenient interface.

## Features

### Multiple AI Integration
Aila sets itself apart by allowing users to pose a question and receive simultaneous answers from a variety of AI models. This unique feature enriches the user experience with a broad spectrum of insights, as each AI contributes its distinct perspective and expertise.

### Supported AI Models
Aila currently supports the following AI models:

| AI Name            | Introduction |
|--------------------|--------------|
| ChatGPT            | An AI developed by OpenAI, known for generating human-like text based on the input provided. |
| Gemini             | A versatile AI capable of performing a variety of tasks, including language translation and data analysis. |
| Claude             | An AI designed for conversational engagement, providing responses that can mimic human dialogue. |
| Copilot            | Your AI companion that helps you navigate information and perform tasks across various domains. |
| Mistral AI         | A large language model called Mistral, which is comparable to GPT-4 in terms of performance. |
| Poe                | An advanced AI chatbot aggregator for seamless conversations and content generation. |
| Coze               | A next-generation AI application and chatbot developing platform for everyone.|
| Perplexity         | An AI that focuses on answering complex questions with detailed explanations. |
| HuggingChat        | An AI built on the Hugging Face platform, known for its natural language processing capabilities. |
| YouAI              | A personalized AI that tailors its responses and recommendations to the individual user's preferences. |
| lmsyshat           | An AI with capabilities in system administration and IT support, providing technical assistance. |
| Gemini Pro 1.5     | An advanced version of Gemini, offering enhanced performance and additional features. |
| Together AI        | An AI that facilitates collaboration and teamwork through shared tasks and project management tools. |
| Grok AI            | An AI that excels in pattern recognition and data interpretation, often used in analytics. |
| ai playground      | A platform for experimenting with different AI models and exploring their functionalities. |
| PI AI              | An AI that specializes in personal intelligence, helping users manage their personal data securely. |
| Devin AI           | An AI that provides development and coding assistance, streamlining the software development process. |
| Tongyi Qianwen     | An AI focused on unifying diverse data sources to provide comprehensive insights. |
| DouBao AI          | An AI that offers financial analysis and predictions, commonly used in the fintech sector. |
| ChatGLM            | An AI that generates text based on generative language models, suitable for various applications. |
| character ai       | An AI that creates character profiles and narratives for storytelling and gaming purposes. |
| meta image generator | An AI that generates images based on textual descriptions, often used for creative projects. |
| Suno AI            | An AI that composes music and generates songs based on user input, enhancing musical creativity. |
| Wenxin Yiyan       | A powerful AI chatbot from Baidu. |
| Kimi Chat          | A powerful AI chatbot from Moonshot AI. |


### Dynamic AI Display
Customize your viewing experience by choosing how many AI responses to display simultaneously. Options include 1, 2, 3, 4, or 6 AI models, allowing flexibility in information presentation.

![image](https://github.com/win4r/AISuperDomain/assets/42172631/3b8cead8-fe71-497a-aff0-f0dddca05eb1)

### Full-Screen Mode
Click on an AI's name in the top-right corner to view its response in full screen, making it easier to focus on and read through. To return to the multi-AI display, simply click the "Home" button.

![image](https://github.com/win4r/AISuperDomain/assets/42172631/36f0d4ac-ce52-44d8-a0b9-5362b3b9c46e)

### Efficient Interaction
- **Prompt Suggestions**: Initiate a query with "/" to access a list of prompt suggestions, facilitating quicker engagement.
- **Persistent Prompts**: Using "#" at the start of your input activates a persistent prompt, keeping it available for continuous use.
- **Flexible Submission**: Submit questions via the Tab+Enter shortcut or by clicking the "Submit" button. The "Switch" button allows for easy cycling through displayed AI models.

### Customization and Configuration
Access settings and customization options through the fly-out menu by clicking the top-left icon. Here, users can select their preferred AI models and adjust the display count. The "Config File" option leads to Aila's JSON configuration file, where users can add new AI models, modify prompts, and personalize their experience.

![image](https://github.com/win4r/AISuperDomain/assets/42172631/30035e18-ce75-49ff-b385-f3895d8dd029)

## Getting Started

To begin using Aila, download the latest version from [Aila Releases](https://github.com/win4r/AISuperDomain/releases). Follow the instructions below based on your operating system to ensure a smooth installation process.

### macOS Installation
After downloading, macOS users can simply double-click the downloaded file to start the installation process. Follow the on-screen instructions to complete the setup.

### Windows Installation
Windows users must ensure that their system has .NET 8 installed prior to installation. Additionally, you will need to install the provided certificate before proceeding with the Aila installation. Once these prerequisites are met, double-click the downloaded file and follow the on-screen instructions to install Aila.

Explore the settings within Aila to customize your AI experience to your liking.


## Customize a script and add any AI

You can start by locating the input box for AI, then use the browser's devtool to inspect the HTML of the input box element. Send the HTML to ChatGPT, and ask ChatGPT to write a generic script for you.
<img width="1385" alt="Screenshot 2024-03-11 at 12 11 34 PM" src="https://github.com/win4r/AISuperDomain/assets/42172631/ee50b1b8-d80e-4e3a-a284-712dbb1d9398">


You can refer to the steps and methods I used to ask ChatGPT, as shared in the link to our conversation.
This step is based on the input box of chat.mistral.ai, allowing ChatGPT to provide a correct and usable script.
https://chat.openai.com/share/835483e9-1ca1-41bb-8858-1c1402e98648

## Contributing

Contributions to Aila are highly encouraged. Whether you have suggestions for new features, improvements, or bug fixes, please feel free to open an issue or submit a pull request.

## License

Aila Desktop Application is released under the MIT License. For more details, see the LICENSE file.

## Contact

For support or feedback, please contact us through the GitHub issues section of this repository. Your feedback is crucial for making Aila an even better tool for interacting with AI.

Thank you for exploring Aila Desktop Application. We look forward to seeing the innovative ways you use this platform to enhance your AI interactions!

## Buy Me a Coffee
[!["Buy Me A Coffee"](https://storage.ko-fi.com/cdn/kofi2.png?v=3)](https://ko-fi.com/aila)

## My WeChat Group and My WeChat QR Code

<img src="https://github.com/win4r/AISuperDomain/assets/42172631/d6dcfd1a-60fa-4b6f-9d5e-1482150a7d95" width="186" height="300">
<img src="https://github.com/win4r/AISuperDomain/assets/42172631/7568cf78-c8ba-4182-aa96-d524d903f2bc" width="214.8" height="291">
<img src="https://github.com/win4r/AISuperDomain/assets/42172631/fefe535c-8153-4046-bfb4-e65eacbf7a33" width="207" height="281">


