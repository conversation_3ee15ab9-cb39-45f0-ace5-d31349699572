string js_bing_image = $@"(function(){{let t;document.querySelector('#sb_form_q').value='[message]';clearTimeout(t);t=setTimeout(()=>{{document.querySelector('#create_btn_c').click();}},1000);}})();";
string js_poe = $@"((textarea = document.querySelector('textarea'), lastValue = textarea.value, textarea.value = '[message]', inputEvent = new Event('input', {{bubbles: true }}), inputEvent.simulated = true, tracker = textarea._valueTracker, tracker ? tracker.setValue(lastValue) : void 0, textarea.dispatchEvent(inputEvent), enterKeyEvent = new KeyboardEvent('keydown', {{key: 'Enter', code: 'Enter', keyCode: 13, which: 13, bubbles: true, cancelable: true }}), textarea.dispatchEvent(enterKeyEvent)));";
string js_bito = $@"document.querySelector('#user_input1').value = '[message]';document.querySelector('#user_input1').dispatchEvent(new KeyboardEvent('keydown', {{ keyCode: 13, bubbles: true }}));";
string js_huggingChat = $@"(document.querySelector('textarea').value = '[message]', document.querySelector('textarea').dispatchEvent(new Event('input', {{bubbles: true }})), document.querySelector('textarea').dispatchEvent(new KeyboardEvent('keydown', {{key: 'Enter' }})))";
string js_bing_update = $@"(document.querySelector('cib-serp').shadowRoot.querySelector('#cib-action-bar-main').shadowRoot.querySelector('cib-text-input').shadowRoot.querySelector('#searchbox').value = '[message]', document.querySelector('cib-serp').shadowRoot.querySelector('#cib-action-bar-main').shadowRoot.querySelector('cib-text-input').shadowRoot.querySelector('#searchbox').dispatchEvent(new Event('input', {{bubbles: true }})), document.querySelector('cib-serp').shadowRoot.querySelector('#cib-action-bar-main').shadowRoot.querySelector('cib-text-input').shadowRoot.querySelector('#searchbox').dispatchEvent(new KeyboardEvent('keydown', {{key: 'Enter' }})));";
string js_bing_compose = $@"(() => {{ document.querySelector('#prompt_text').value = '[message]'; document.querySelector('#prompt_text').dispatchEvent(new Event('input', {{bubbles: true}})); setTimeout(() => document.querySelector('#compose_button').click(), 500); }})();";

//string js_bing = $@"(document.querySelector('cib-serp').shadowRoot.querySelector('#cib-action-bar-main').shadowRoot.querySelector('#searchbox').value = '[message]', document.querySelector('cib-serp').shadowRoot.querySelector('#cib-action-bar-main').shadowRoot.querySelector('#searchbox').dispatchEvent(new Event('input', {{bubbles: true }})), document.querySelector('cib-serp').shadowRoot.querySelector('#cib-action-bar-main').shadowRoot.querySelector('#searchbox').dispatchEvent(new KeyboardEvent('keydown', {{key: 'Enter' }})))";
string js_bard = $@"(function(p, btn) {{ p.innerText = '[message]', setTimeout(function() {{ btn.click(); }}, 500); }})(document.querySelector('.ql-editor.textarea > p'), document.querySelector('.send-button.mdc-icon-button'));";
string js_lmsys = $@"(function() {{ var buttons = document.querySelectorAll('button.svelte-kqij2n'); if(buttons.length >= 3) {{ buttons[2].click(); }} var elements = document.querySelectorAll('.svelte-1ed2p3z'); elements.forEach(function(element) {{ element.style.display = 'none'; }}); var textareas = document.querySelectorAll('textarea[data-testid=""textbox""]'); var buttons = [document.querySelector('#component-28'), document.querySelector('#component-65'), document.querySelector('#component-89')]; textareas.forEach(function(textarea) {{ textarea.value = '[message]'; textarea.dispatchEvent(new Event('input', {{ bubbles: true }})); }}); setTimeout(function() {{ buttons.forEach(function(button) {{ if(button) button.click(); }}); }}, 1000); }})();";
string js_BLOOM = $@"((textarea = document.querySelector('textarea'), lastValue = textarea.value, textarea.value = '[message]', inputEvent = new Event('input', {{bubbles: true }}), inputEvent.simulated = true, tracker = textarea._valueTracker, tracker ? tracker.setValue(lastValue) : void 0, textarea.dispatchEvent(inputEvent), enterKeyEvent = new KeyboardEvent('keydown', {{key: 'Enter', code: 'Enter', keyCode: 13, which: 13, bubbles: true, cancelable: true }}), textarea.dispatchEvent(enterKeyEvent)));";
//string js_StableLM = $@"((t) => (t.value = '[message]', t.dispatchEvent(new Event('input', {{bubbles: true}})), t.dispatchEvent(new KeyboardEvent('keydown', {{key: 'Enter'}})), document.querySelector('#component-10').click()))(document.querySelector('textarea'));";
//string js_theb = $@"document.querySelector('textarea').value = '[message]';document.querySelector('textarea').dispatchEvent(new Event('input',{{bubbles:!0}}));document.querySelector('textarea').dispatchEvent(new KeyboardEvent('keydown',{{keycode:13}}));setTimeout(function(){{document.querySelector('.n-button__icon').click();document.querySelectorAll('button')[24].click()}},1e3);";
string js_openai = $@"(function() {{ var textarea = document.querySelector('textarea'); textarea.value = '[message]'; textarea.dispatchEvent(new Event('input', {{bubbles: true}})); setTimeout(function() {{ textarea.dispatchEvent(new KeyboardEvent('keydown', {{key: 'Enter', code: 'Enter', keyCode: 13, charCode: 13, which: 13, bubbles: true, cancelable: true}})); }}, 1000); }})();";
string js_slackClaude = $@"(function(){{let myDiv=document.querySelector('.ql-editor');let myP=myDiv.querySelector('p');let button=document.querySelector('.c-button-unstyled.c-icon_button.c-icon_button--size_small.c-wysiwyg_container__button.c-wysiwyg_container__button--send.c-icon_button--default');myP.textContent='[message]';let inputEvent=new Event('input',{{bubbles:true}});myP.dispatchEvent(inputEvent);myDiv.addEventListener('input',function(e){{}});setTimeout(function(){{button.click();}},1000);}})();";

string js_llama = $@"((textarea = document.querySelector('textarea'), lastValue = textarea.value, textarea.value = '[message]', inputEvent = new Event('input', {{bubbles: true }}), inputEvent.simulated = true, tracker = textarea._valueTracker, tracker ? tracker.setValue(lastValue) : void 0, textarea.dispatchEvent(inputEvent), enterKeyEvent = new KeyboardEvent('keydown', {{key: 'Enter', code: 'Enter', keyCode: 13, which: 13, bubbles: true, cancelable: true }}), textarea.dispatchEvent(enterKeyEvent)));";

//string js_claude = $@"(function(){{document.querySelector('.is-empty.is-editor-empty').innerHTML = '[message]'; setTimeout(function() {{ var elem = document.querySelector('.ProseMirror.p-4'); if (elem) {{ var event = new KeyboardEvent('keydown', {{ 'key': 'Enter', 'code': 'Enter', 'keyCode': 13, 'which': 13, 'bubbles': true, 'cancelable': true }}); elem.dispatchEvent(event); }} else {{ console.log(""Element not found""); }} }}, 1000);}}());";

string js_claude2 = $@"(function(){{document.querySelector('.is-empty.is-editor-empty').innerHTML = '[message]'; setTimeout(function(){{var elem = document.querySelector('.ProseMirror p'); if (elem) {{var event = new KeyboardEvent('keydown', {{'key': 'Enter', 'code': 'Enter', 'keyCode': 13, 'which': 13, 'bubbles': true, 'cancelable': true}}); setTimeout(function(){{elem.dispatchEvent(event);}}, 500); setTimeout(function(){{var buttonElement = document.querySelector('button[aria-label=""Send Message""]'); if (buttonElement) {{buttonElement.click();}}}}, 1000);}}}}, 1000);}}());";

string js_perplexityAI_characterAI = $@"(function(){{let a=document.querySelector('textarea'),b=Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype,""value"").set;b.call(a,'[message]');let c=new Event('input',{{bubbles:true}});a.dispatchEvent(c);let d=new KeyboardEvent('keydown',{{bubbles:true,key:'Enter',keyCode:13}});a.dispatchEvent(d)}})();document.querySelector('.aspect-square.h-10').click();";

private string js_ImaginewithMetaAI = $@"(function() {{ let textarea = document.querySelector('textarea'); let nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, ""value"").set; nativeInputValueSetter.call(textarea, '[message]'); let inputEvent = new Event('input', {{ bubbles: true}}); textarea.dispatchEvent(inputEvent); let enterEvent = new KeyboardEvent('keydown', {{ bubbles: true, key: 'Enter', keyCode: 13 }}); textarea.dispatchEvent(enterEvent); }})();";


string js_qianwen = $@"(function(){{let a=document.querySelector('textarea'),b=Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype,'value').set;b.call(a,'[message]');a.dispatchEvent(new Event('input',{{bubbles:true}}));a.dispatchEvent(new KeyboardEvent('keydown',{{bubbles:true,key:'Enter',keyCode:13}}));}})();";
string js_yiyan = $@"(function(){{let a=document.querySelector('textarea'),b=Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype,'value').set;b.call(a,'[message]');a.dispatchEvent(new Event('input',{{bubbles:true}}));a.dispatchEvent(new KeyboardEvent('keydown',{{bubbles:true,key:'Enter',keyCode:13}}));document.querySelector("".VAtmtpqL"").click();}})();";
string js_tiangong = $@"(function() {{ document.querySelector('textarea').value = '[message]'; document.querySelector('textarea').dispatchEvent(new Event('input', {{ bubbles: true }})); document.querySelector('textarea').dispatchEvent(new KeyboardEvent('keydown', {{ key: 'Enter' }})); document.querySelector('.sureSubmitDiv').click(); }})();";
string js_xinghuo = $@"(function(){{ let a=document.querySelector('div#ask-window > textarea'),b=Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype,'value').set;b.call(a,'[message]');a.dispatchEvent(new Event('input',{{ bubbles:true }}));a.dispatchEvent(new KeyboardEvent('keydown',{{ bubbles:true,key:'Enter',keyCode:13 }}));document.querySelector('div#ask-window > div.ask-window_send__xTavC').click(); }})();";
string js_chatglm = $@"(function(){{var t=document.querySelector('textarea'),l=t.value,i=new Event('input',{{bubbles:true}});t.value='[message]',i.simulated=true,t._valueTracker&&t._valueTracker.setValue(l),t.dispatchEvent(i),t.dispatchEvent(new KeyboardEvent('keydown',{{key:'Enter',code:'Enter',keyCode:13,which:13,bubbles:true,cancelable:true}})),setTimeout(function(){{document.querySelector('div#search-input-box img').click();}},1000);}})();";
string js_doubao = $@"(function(){{ let a=document.querySelector('div#root textarea'),b=Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype,'value').set;b.call(a,'[message]');a.dispatchEvent(new Event('input',{{ bubbles:true }}));a.dispatchEvent(new KeyboardEvent('keydown',{{ bubbles:true,key:'Enter',keyCode:13 }})); }})();";

string js_GeminiPro = $@"(function() {{ var editor = document.querySelector('.ql-editor'); if (editor) {{ editor.textContent += '\\r\\n[message]\\r\\n'; }} setTimeout(function() {{ var runButton = document.querySelector('.run-button'); if (runButton) {{ runButton.click(); }} }}, 1000); }})();";

string js_baiduAI = $@"((t) => {{ if(t) {{ t.value = '[message]'; t.dispatchEvent(new Event('input', {{bubbles: true}})); t.dispatchEvent(new KeyboardEvent('keydown', {{key: 'Enter', bubbles: true, cancelable: true}})); setTimeout(() => {{ const s = document.querySelector('.c-icon.text-input-send_2GgoL'); s && s.click(); }}, 1000); }} }})(document.querySelector('textarea'));";

string js_perplexity_lab = $@"((textarea = document.querySelector('textarea'), lastValue = textarea.value, textarea.value = '[message]', inputEvent = new Event('input', {{bubbles: true }}), inputEvent.simulated = true, tracker = textarea._valueTracker, tracker ? tracker.setValue(lastValue) : void 0, textarea.dispatchEvent(inputEvent), enterKeyEvent = new KeyboardEvent('keydown', {{key: 'Enter', code: 'Enter', keyCode: 13, which: 13, bubbles: true, cancelable: true }}), textarea.dispatchEvent(enterKeyEvent)));";

const string js_falcon_180b = @"((t) => (t.value = '[message]', t.dispatchEvent(new Event('input', {bubbles: true})), t.dispatchEvent(new KeyboardEvent('keydown', {key: 'Enter'})), document.querySelector('#component-17').click()))(document.querySelector('textarea'));";

const string ini_js_lmsys = @"(function() { var intervalId = setInterval(function() { var elements = document.querySelectorAll('.svelte-1ed2p3z'); if(elements.length > 0) { elements.forEach(function(element) { element.style.display = 'none'; }); clearInterval(intervalId); } }, 2000); })();";


// const string _ini_js_bingUnOfficial = "(function(){var checkElement = setInterval(function() { var elem = document.querySelector('.n-notification-container.n-notification-container--scrollable.n-notification-container--top-right'); if (elem) { elem.style.display = 'none'; clearInterval(checkElement); } }, 1000);}());";
const string _ini_js_poe = "";
const string _ini_js_perplexity_lab = "(function() {let select = document.getElementById('lamma-select'); select.value = '[item]'; let event = new Event('change', { bubbles: true, cancelable: true });select.dispatchEvent(event);})();";
const string _ini_js_openai = "(document.querySelector('.flex-shrink-0.overflow-x-hidden.dark.bg-gray-900') && window.getComputedStyle(document.querySelector('.flex-shrink-0.overflow-x-hidden.dark.bg-gray-900')).visibility === 'visible') && (function() { var rectElement = document.querySelector('rect'); var event = new MouseEvent('click', { 'view': window, 'bubbles': true, 'cancelable': true }); rectElement.dispatchEvent(event); })();";
const string _ini_js_bloom = "document.querySelector('header.MuiBox-root.mui-style-x4qroo').style.display = 'none';document.querySelector('div.MuiBox-root.mui-style-1mrd89u').style.display = 'none';";
const string _ini_js_Bito = "document.getElementById('bitoai_title_content1').style.display = 'none';document.getElementById('signedInDivWeb').style.display = 'none'; document.querySelector('div[style*='text-align: center;font-size: 13px;color: #fff;padding-bottom: 5px']').style.display = 'none';";
const string _ini_js_freechatgpt = "(function() { Array.from(document.getElementsByClassName('info')).forEach(function(element) { element.style.display = 'none'; }); })();";
const string _ini_js_LLAM2 = $@"(function() {{ var intervalId = setInterval(function() {{ var tabNav = document.querySelector('.tab-nav'), noticeMarkdown = document.querySelector('#notice_markdown'), component25 = document.querySelector('#component-25'), builtWithSvelte = document.querySelector('.built-with.svelte-1lyswbr'); if (tabNav && noticeMarkdown && component25 && builtWithSvelte) {{ tabNav.style.display = 'none', noticeMarkdown.style.display = 'none', component25.style.display = 'none', builtWithSvelte.style.display = 'none', clearInterval(intervalId); }} }}, 1000); }})();";
const string _ini_baiduAi = $@"(() => setTimeout(() => document.querySelector('.ai-entry-right').click(), 3000))();";
const string _ini_falcon_180b = $@"(function check() {{ var el = document.querySelector('#component-6'); el ? el.style.display = 'none' : setTimeout(check, 1000); }})();";

const string js_poe_getContent = @"(() => { let elements = document.querySelectorAll('.Markdown_markdownContainer__Tz3HQ'); return elements[elements.length - 1].textContent; })()";

const string js_claude2_getContent = @"(() => { let elements = document.querySelectorAll('.contents'); return elements[elements.length - 2].textContent; })()";

const string js_bard_getContent = @"(() => { let elements = document.querySelectorAll('.markdown.markdown-main-panel'); return elements[elements.length - 1].textContent; })()";

const string js_llama2_getContent = @"(() => { let elements = document.querySelectorAll('.text-sm'); return elements[elements.length - 1].textContent; })()";

const string js_openai_getContent = @"(() => (document.querySelectorAll('.markdown.prose')[document.querySelectorAll('.markdown.prose').length - 1].textContent))();";
