<div align="center">
  <p><strong>모든 주요 AI와 한 번에 접속하여 최종 통찰을 밝혀내다</strong></p>

 [영어](README.md) | [简体中文](README_ZH-CN.md) | [繁體中文](README_ZH-TW.md) | [日本語](README_JA-JP.md) | 한국어 | [Français](README_FR-FR.md)

[![GitHub Codespaces에서 열기](https://github.com/codespaces/badge.svg)](https://github.com/win4r/AISuperDomain/releases)

</div>

![이미지](https://github.com/win4r/AISuperDomain/assets/42172631/2115997d-8b00-4767-bf79-103b4e53abc3)


# AI超元域 데스크탑 어플리케이션

AI超元域 데스크탑 어플리케이션 GitHub 저장소에 오신 것을 환영합니다! 이 혁신적인 소프트웨어는 단일 데스크탑 어플리케이션으로 여러 선도적인 AI 모델을 통합하여 사용자가 질문에 대한 다양한 응답을 생성할 수 있는 강력한 플랫폼을 제공합니다. AI超元域은 Windows 및 macOS 시스템에서 원활하게 실행되도록 설계되어 사용자가 하나의 편리한 인터페이스를 통해 여러 인공지능과 상호 작용할 수 있습니다.

## 기능

### 다중 AI 통합
AI超元域은 사용자가 질문을 하면 다양한 AI 모델에서 동시에 답변을 받을 수 있게 함으로써 사용자 경험을 넓은 통찰력의 스펙트럼으로 풍부하게 합니다. 각 AI는 고유한 관점과 전문 지식을 제공합니다.

### 지원되는 AI 모델
AI超元域은 현재 다음 AI 모델을 지원합니다:

| AI 이름            | 소개 |
|--------------------|--------------|
| ChatGPT            | OpenAI에서 개발한 AI로, 제공된 입력을 바탕으로 인간과 같은 텍스트를 생성하는 것으로 알려져 있습니다. |
| Gemini             | 언어 번역 및 데이터 분석을 포함한 다양한 작업을 수행할 수 있는 다재다능한 AI입니다. |
| Claude             | 대화 참여를 위해 설계된 AI로, 인간 대화를 모방할 수 있는 응답을 제공합니다. |
| Copilot            | 다양한 분야에서 정보 탐색 및 작업 수행을 도와주는 AI 동반자입니다. |
| Poe                | 원활한 대화와 콘텐츠 생성을 위한 고급 AI 챗봇 집합기입니다. |
| Perplexity         | 상세한 설명이 필요한 복잡한 질문에 답하는 데 중점을 둔 AI입니다. |
| HuggingChat        | 자연어 처리 기능으로 알려진 Hugging Face 플랫폼 위에 구축된 AI입니다. |
| YouAI              | 개별 사용자의 선호도에 맞춰 응답 및 추천을 맞춤 설정하는 개인화된 AI입니다. |
| lmsyshat           | 시스템 관리 및 IT 지원에서 능력을 발휘하는 AI로, 기술적 지원을 제공합니다. |
| Gemini Pro 1.5     | 향상된 성능과 추가 기능을 제공하는 Gemini의 고급 버전입니다. |
| Together AI        | 공유 작업 및 프로젝트 관리 도구를 통해 협업과 팀워크를 촉진하는 AI입니다. |
| Grok AI            | 패턴 인식 및 데이터 해석에 뛰어난 AI로, 주로 분석 분야에 사용됩니다. |
| ai playground      | 다양한 AI 모델을 실험하고 기능을 탐색할 수 있는 플랫폼입니다. |
| PI AI              | 개인 정보를 안전하게 관리하는 데 도움을 주는 개인 지능 전문 AI입니다. |
| Devin AI           | 개발 및 코딩 지원을 제공하는 AI로, 소프트웨어 개발 과정을 간소화합니다. |
| Tongyi Qianwen     | 다양한 데이터 소스를 통합하여 포괄적인 통찰력을 제공하는 데 중점을 둔 AI입니다. |
| DouBao AI          | 금융 분석 및 예측을 제공하는 AI로, 주로 핀테크 분야에서 사용됩니다. |
| ChatGLM            | 생성 언어 모델을 기반으로 텍스트를 생성하는 AI로, 다양한 응용 프로그램에 적합합니다. |
| character ai       | 스토리텔링 및 게임 목적으로 캐릭터 프로필 및 서사를 생성하는 AI입니다. |
| meta image generator | 텍스트 설명을 기반으로 이미지를 생성하는 AI로, 주로 창의적 프로젝트에 사용됩니다. |
| Suno AI            | 사용자 입력을 기반으로 음악을 작곡하고 노래를 생성하는 AI로, 음악 창의력을 증진시킵니다. |


### 동적 AI 디스플레이
동시에 표시되는 AI 응답의 수를 선택하여 보는 경험을 사용자 정의할 수 있습니다. 1, 2, 3, 4 또는 6 AI 모델을 표시하는 옵션이 있어 정보 표시 방식에 유연성을 제공합니다.

### 전체 화면 모드
오른쪽 상단 모서리에 있는 AI의 이름을 클릭하면 해당 응답을 전체 화면으로 볼 수 있어 집중해서 읽기가 더 쉬워집니다. 다중 AI 디스플레이로 돌아가려면 단순히 "Home" 버튼을 클릭하면 됩니다.

### 효율적인 상호 작용
- **프롬프트 제안**: "/"로 쿼리를 시작하여 프롬프트 제안 목록에 접근함으로써 더 빠르게 참여할 수 있습니다.
- **지속적인 프롬프트**: 입력의 시작에 "#"을 사용하면 지속적인 프롬프트가 활성화되어 계속 사용할 수 있습니다.
- **유연한 제출**: Tab+Enter 단축키를 사용하거나 "Submit" 버튼을 클릭하여 질문을 제출할 수 있습니다. 표시된 AI 모델을 쉽게 순환할 수 있는 "Switch" 버튼이 있습니다.

### 사용자 정의 및 구성
왼쪽 상단 아이콘을 클릭하여 나타나는 플라이 아웃 메뉴를 통해 설정 및 사용자 정의 옵션에 액세스할 수 있습니다. 여기에서 사용자는 선호하는 AI 모델을 선택하고 디스플레이 수를 조정할 수 있습니다. "Config File" 옵션은 AI超元域의 JSON 구성 파일로 이어지며, 여기에서 사용자는 새로운 AI 모델을 추가하거나 프롬프트를 수정하고 경험을 개인화할 수 있습니다.

## 시작하기

AI超元域을 사용하기 시작하려면 [AI超元域 릴리스](https://github.com/win4r/AISuperDomain/releases)에서 최신 버전을 다운로드하십시오. 원활한 설치 프로세스를 보장하기 위해 운영 체제에 따라 아래 지침을 따르십시오.

### macOS 설치
다운로드 후 macOS 사용자는 다운로드한 파일을 더블 클릭하여 설치 프로세스를 시작할 수 있습니다. 화면의 지시에 따라 설정을 완료하십시오.

### Windows 설치
Windows 사용자는 설치하기 전에 시스템에 .NET 8이 설치되어 있는지 확인해야 합니다. 또한 AI超元域 설치를 진행하기 전에 제공된 인증서를 설치해야 합니다. 이러한 전제 조건이 충족되면 다운로드한 파일을 더블 클릭하고 화면의 지시에 따라 AI超元域을 설치하십시오.

AI超元域 내의 설정을 탐색하여 AI 경험을 원하는 대로 사용자 정의하십시오.

## 기여

AI超元域에 대한 기여를 환영합니다. 새로운 기능, 개선 사항 또는 버그 수정에 대한 제안이 있으시면 문제를 열거나 풀 리퀘스트를 제출해 주십시오.

## 라이센스

AI超元域 데스크탑 어플리케이션은 MIT 라이센스에 따라 배포됩니다. 자세한 내용은 LICENSE 파일을 참조하십시오.

## 연락처

지원이 필요하거나 피드백을 공유하고 싶으시면 이 저장소의 GitHub 문제 섹션을 통해 저희에게 연락해 주십시오. 여러분의 피드백은 AI와 상호 작용하는 데 있어 AI超元域을 더욱 좋은 도구로 만드는 데 중요합니다.

AI超元域 데스크탑 어플리케이션을 탐색해 주셔서 감사합니다. 이 플랫폼을 사용하여 AI 상호 작용을 향상시키는 혁신적인 방법을 보게 되기를 기대합니다!
